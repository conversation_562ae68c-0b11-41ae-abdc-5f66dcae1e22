import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { apiService } from '../services/apiService';
import { AdvancedSearchBar } from '../components/SearchBar';
import { ModernCard } from '../components/ModernCard';
import { ModernButton } from '../components/ModernButton';
import { StatusChip } from '../components/ModernChip';
import { ListSkeleton, HeaderSkeleton, SearchBarSkeleton } from '../components/ModernLoading';
import { theme } from '../theme/theme';
import { useAuth } from '../context/AuthContext';

export default function ServicesScreen({ navigation }) {
  const { loading: authLoading, isAuthenticated, token, user } = useAuth();
  const [services, setServices] = useState([]);
  const [filteredServices, setFilteredServices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilters, setSelectedFilters] = useState([]);

  useEffect(() => {
    // Only load services when authentication is complete and user is authenticated
    if (!authLoading && isAuthenticated && token) {
      console.log('🔄 Auth state ready, loading services...');
      // Add a small delay to ensure token is properly set
      const timer = setTimeout(() => {
        loadServices();
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [authLoading, isAuthenticated, token]);

  useEffect(() => {
    filterServices();
  }, [services, searchQuery, selectedFilters]);

  const filterServices = () => {
    let filtered = [...services];

    // Apply text search
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(service =>
        service.title.toLowerCase().includes(query) ||
        service.description.toLowerCase().includes(query) ||
        (service.category && service.category.toLowerCase().includes(query))
      );
    }

    // Apply status filters
    if (selectedFilters.length > 0) {
      filtered = filtered.filter(service =>
        selectedFilters.includes(service.status)
      );
    }

    setFilteredServices(filtered);
  };

  const handleSearch = (query, filters) => {
    setSearchQuery(query);
    setSelectedFilters(filters);
  };

  const loadServices = async () => {
    try {
      setLoading(true);
      console.log('🔄 Loading services...');

      // Check if we have a token and ensure it's set in the API service
      if (user && !apiService.getAuthStatus().hasToken && token) {
        console.log('⚠️ User is logged in but API service has no token, attempting to restore...');
        console.log('🔑 Restoring token from AuthContext for services');
        apiService.setAuthToken(token);
      }

      const response = await apiService.getServicesWithCache();
      if (response.success) {
        console.log('✅ Services loaded successfully:', response.data.services?.length || response.data?.length || 0, 'services');
        setServices(response.data.services || response.data);
      } else {
        console.error('❌ Failed to load services:', response.message);
        Alert.alert('Error', 'Failed to load services');
      }
    } catch (error) {
      console.error('Error loading services:', error);
      Alert.alert('Error', 'Failed to load services');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadServices();
  };

  const getIconName = (iconClass) => {
    // Convert FontAwesome class to Ionicons name
    const iconMap = {
      'fas fa-tools': 'construct',
      'fas fa-hard-hat': 'shield',
      'fas fa-hammer': 'hammer',
      'fas fa-wrench': 'build',
      'fas fa-cogs': 'settings',
      'fas fa-building': 'business',
      'fas fa-home': 'home',
      'fas fa-industry': 'business',
      'fas fa-truck': 'car',
      'fas fa-tractor': 'car-sport',
      'fas fa-paint-roller': 'brush',
      'fas fa-ruler-combined': 'resize',
      'fas fa-drafting-compass': 'compass',
      'fas fa-layer-group': 'layers',
    };
    return iconMap[iconClass] || 'construct';
  };

  const renderService = ({ item }) => (
    <ModernCard
      style={styles.serviceCard}
      onPress={() => navigation.navigate('ServiceDetail', { service: item })}
      elevation="medium"
      animated={true}
      pressScale={0.98}
    >
      <View style={styles.serviceHeader}>
        <View style={[styles.iconContainer, { backgroundColor: theme.colors.primaryContainer }]}>
          <Ionicons
            name={getIconName(item.icon)}
            size={28}
            color={theme.colors.primary}
          />
        </View>
        <View style={styles.serviceInfo}>
          <Text style={styles.serviceTitle}>{item.title}</Text>
          <Text style={styles.serviceCategory} numberOfLines={1}>
            {item.category || 'General Service'}
          </Text>
        </View>
        <StatusChip
          status={item.status === 'active' ? 'completed' : 'planned'}
        />
      </View>

      <Text style={styles.serviceDescription} numberOfLines={3}>
        {item.description}
      </Text>

      <View style={styles.serviceFooter}>
        <View style={styles.serviceMetrics}>
          <View style={styles.metricItem}>
            <Ionicons name="time-outline" size={16} color={theme.colors.onSurfaceVariant} />
            <Text style={styles.metricText}>Available 24/7</Text>
          </View>
        </View>
        <TouchableOpacity
          style={styles.viewButton}
          onPress={() => navigation.navigate('ServiceDetail', { service: item })}
        >
          <Ionicons name="arrow-forward" size={16} color={theme.colors.primary} />
        </TouchableOpacity>
      </View>
    </ModernCard>
  );

  // Show loading while authentication is in progress
  if (authLoading) {
    return (
      <View style={styles.centerContainer}>
        <Text>Authenticating...</Text>
      </View>
    );
  }

  // Show loading while services are being fetched
  if (loading && !refreshing) {
    return (
      <View style={styles.container}>
        <HeaderSkeleton />
        <SearchBarSkeleton />
        <ListSkeleton count={4} />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={theme.gradients.primary}
        style={styles.header}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <View style={styles.headerContent}>
          <View style={styles.headerTextContainer}>
            <Text style={styles.headerTitle}>Services</Text>
            <Text style={styles.headerSubtitle}>
              {filteredServices.length} {filteredServices.length === 1 ? 'service' : 'services'}
            </Text>
          </View>
          <ModernButton
            icon="add"
            variant="tonal"
            size="medium"
            onPress={() => navigation.navigate('AddService')}
            style={styles.addButton}
          />
        </View>
      </LinearGradient>

      {/* Search and Filter */}
      <View style={styles.searchContainer}>
        <AdvancedSearchBar
          placeholder="Search services..."
          onSearch={handleSearch}
          filters={[
            { label: 'Active', value: 'active', icon: 'checkmark-outline' },
            { label: 'Inactive', value: 'inactive', icon: 'close-outline' },
          ]}
          selectedFilters={selectedFilters}
        />
      </View>

      <FlatList
        data={filteredServices}
        renderItem={renderService}
        keyExtractor={(item) => item.id.toString()}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <View style={styles.emptyIconContainer}>
              <LinearGradient
                colors={theme.gradients.primarySubtle}
                style={styles.emptyIconGradient}
              >
                <Ionicons name="construct-outline" size={64} color={theme.colors.primary} />
              </LinearGradient>
            </View>
            <Text style={styles.emptyText}>No services found</Text>
            <Text style={styles.emptySubtext}>
              {searchQuery || selectedFilters.length > 0
                ? 'Try adjusting your search or filters'
                : 'Start by adding your first service offering'
              }
            </Text>
            {(!searchQuery && selectedFilters.length === 0) && (
              <ModernButton
                title="Add First Service"
                variant="filled"
                size="large"
                onPress={() => navigation.navigate('AddService')}
                icon="add-outline"
                style={styles.emptyButton}
              />
            )}
          </View>
        }
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    paddingTop: theme.spacing.xl,
    paddingBottom: theme.spacing.lg,
    paddingHorizontal: theme.spacing.md,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTextContainer: {
    flex: 1,
  },
  headerTitle: {
    ...theme.typography.headlineMedium,
    color: 'white',
    fontWeight: '700',
  },
  headerSubtitle: {
    ...theme.typography.bodyMedium,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: theme.spacing.xs,
  },
  addButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  searchContainer: {
    backgroundColor: theme.colors.surface,
    paddingHorizontal: theme.spacing.md,
    paddingBottom: theme.spacing.md,
    ...theme.shadows.xs,
  },
  listContainer: {
    padding: theme.spacing.md,
  },
  serviceCard: {
    marginBottom: theme.spacing.lg,
    marginHorizontal: theme.spacing.xs,
  },
  serviceHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  iconContainer: {
    width: 56,
    height: 56,
    borderRadius: theme.borderRadius.lg,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: theme.spacing.md,
  },
  serviceInfo: {
    flex: 1,
  },
  serviceTitle: {
    ...theme.typography.titleLarge,
    color: theme.colors.onSurface,
    fontWeight: '600',
    marginBottom: theme.spacing.xs,
  },
  serviceCategory: {
    ...theme.typography.bodyMedium,
    color: theme.colors.onSurfaceVariant,
  },
  serviceDescription: {
    ...theme.typography.bodyMedium,
    color: theme.colors.onSurface,
    lineHeight: 22,
    marginBottom: theme.spacing.md,
  },
  serviceFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: theme.spacing.sm,
  },
  serviceMetrics: {
    flex: 1,
  },
  metricItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  metricText: {
    ...theme.typography.labelSmall,
    color: theme.colors.onSurfaceVariant,
    marginLeft: theme.spacing.xs,
  },
  viewButton: {
    width: 32,
    height: 32,
    borderRadius: theme.borderRadius.full,
    backgroundColor: theme.colors.primaryContainer,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: theme.spacing.xxxxl,
    paddingHorizontal: theme.spacing.xl,
  },
  emptyIconContainer: {
    marginBottom: theme.spacing.xl,
  },
  emptyIconGradient: {
    width: 120,
    height: 120,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
    ...theme.shadows.medium,
  },
  emptyText: {
    ...theme.typography.headlineSmall,
    color: theme.colors.onSurface,
    textAlign: 'center',
    marginBottom: theme.spacing.sm,
  },
  emptySubtext: {
    ...theme.typography.bodyLarge,
    color: theme.colors.onSurfaceVariant,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: theme.spacing.xl,
    maxWidth: 280,
  },
  emptyButton: {
    marginTop: theme.spacing.md,
    minWidth: 200,
  },
});
