{"name": "FloriConstructionAdmin", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^13.0.0", "@react-native-community/netinfo": "9.3.10", "@react-navigation/bottom-tabs": "^6.5.7", "@react-navigation/native": "^6.1.6", "@react-navigation/native-stack": "^6.9.12", "axios": "^1.4.0", "expo": "~49.0.0", "expo-camera": "~13.4.4", "expo-constants": "~14.4.2", "expo-device": "~5.4.0", "expo-image-picker": "~14.3.2", "expo-linear-gradient": "~12.3.0", "expo-notifications": "~0.20.1", "expo-secure-store": "~12.3.1", "expo-status-bar": "~1.6.0", "react": "18.2.0", "react-native": "0.72.10", "react-native-paper": "^5.8.0", "react-native-safe-area-context": "4.6.3", "react-native-screens": "~3.22.0", "react-native-vector-icons": "^9.2.0"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}