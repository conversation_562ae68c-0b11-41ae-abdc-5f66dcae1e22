{"expo": {"name": "Flori Construction Admin", "slug": "flori-construction-admin", "version": "1.0.0", "orientation": "portrait", "userInterfaceStyle": "light", "splash": {"resizeMode": "contain", "backgroundColor": "#ff6b35"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"backgroundColor": "#ff6b35"}, "permissions": ["android.permission.RECORD_AUDIO", "android.permission.RECEIVE_BOOT_COMPLETED", "android.permission.VIBRATE", "android.permission.WAKE_LOCK"], "package": "com.masteri.floriconstructionadmin"}, "web": {}, "plugins": [["expo-image-picker", {"photosPermission": "The app accesses your photos to let you upload project images.", "cameraPermission": "The app accesses your camera to let you take project photos."}], ["expo-notifications", {"icon": "./assets/notification-icon.png", "color": "#ff6b35", "sounds": ["./assets/notification-sound.wav"]}]], "extra": {"eas": {"projectId": "25a2756c-48e6-4a27-850e-85ce92f96a80"}}, "owner": "masteri"}}