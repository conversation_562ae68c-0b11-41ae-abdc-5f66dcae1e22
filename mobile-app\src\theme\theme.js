import { DefaultTheme } from 'react-native-paper';

// Modern color palette following Material Design 3 principles
const lightColors = {
  // Primary colors - Modern orange/coral palette
  primary: '#FF6B35',
  primaryContainer: '#FFE8E0',
  onPrimary: '#FFFFFF',
  onPrimaryContainer: '#8B2500',

  // Secondary colors - Complementary blue-gray
  secondary: '#6B7280',
  secondaryContainer: '#F3F4F6',
  onSecondary: '#FFFFFF',
  onSecondaryContainer: '#374151',

  // Tertiary colors - Accent teal
  tertiary: '#0891B2',
  tertiaryContainer: '#E0F7FA',
  onTertiary: '#FFFFFF',
  onTertiaryContainer: '#0F4C75',

  // Surface colors
  surface: '#FFFFFF',
  surfaceVariant: '#F8FAFC',
  surfaceTint: '#FF6B35',
  onSurface: '#1F2937',
  onSurfaceVariant: '#6B7280',

  // Background colors
  background: '#FAFBFC',
  onBackground: '#1F2937',

  // Outline colors
  outline: '#E5E7EB',
  outlineVariant: '#F3F4F6',

  // State colors
  error: '#EF4444',
  errorContainer: '#FEF2F2',
  onError: '#FFFFFF',
  onErrorContainer: '#7F1D1D',

  success: '#10B981',
  successContainer: '#ECFDF5',
  onSuccess: '#FFFFFF',
  onSuccessContainer: '#064E3B',

  warning: '#F59E0B',
  warningContainer: '#FFFBEB',
  onWarning: '#FFFFFF',
  onWarningContainer: '#78350F',

  info: '#3B82F6',
  infoContainer: '#EFF6FF',
  onInfo: '#FFFFFF',
  onInfoContainer: '#1E3A8A',

  // Legacy colors for backward compatibility
  primaryLight: '#FF8A5C',
  primaryDark: '#E55A2B',
  accent: '#F39C12',
  accentLight: '#F5B041',
  backgroundSecondary: '#FFFFFF',
  text: '#1F2937',
  textSecondary: '#6B7280',
  textLight: '#9CA3AF',
  disabled: '#D1D5DB',
  placeholder: '#9CA3AF',
  backdrop: 'rgba(0, 0, 0, 0.5)',
  notification: '#FF6B35',
  errorLight: '#FEF2F2',
  successLight: '#ECFDF5',
  warningLight: '#FFFBEB',
  infoLight: '#EFF6FF',
  light: '#F8FAFC',
  dark: '#1F2937',
  border: '#E5E7EB',
  borderLight: '#F3F4F6',
  overlay: 'rgba(0, 0, 0, 0.1)',
};

// Dark theme colors
const darkColors = {
  // Primary colors
  primary: '#FF8A5C',
  primaryContainer: '#8B2500',
  onPrimary: '#000000',
  onPrimaryContainer: '#FFE8E0',

  // Secondary colors
  secondary: '#9CA3AF',
  secondaryContainer: '#374151',
  onSecondary: '#000000',
  onSecondaryContainer: '#F3F4F6',

  // Tertiary colors
  tertiary: '#22D3EE',
  tertiaryContainer: '#0F4C75',
  onTertiary: '#000000',
  onTertiaryContainer: '#E0F7FA',

  // Surface colors
  surface: '#1F2937',
  surfaceVariant: '#374151',
  surfaceTint: '#FF8A5C',
  onSurface: '#F9FAFB',
  onSurfaceVariant: '#D1D5DB',

  // Background colors
  background: '#111827',
  onBackground: '#F9FAFB',

  // Outline colors
  outline: '#4B5563',
  outlineVariant: '#374151',

  // State colors
  error: '#F87171',
  errorContainer: '#7F1D1D',
  onError: '#000000',
  onErrorContainer: '#FEF2F2',

  success: '#34D399',
  successContainer: '#064E3B',
  onSuccess: '#000000',
  onSuccessContainer: '#ECFDF5',

  warning: '#FBBF24',
  warningContainer: '#78350F',
  onWarning: '#000000',
  onWarningContainer: '#FFFBEB',

  info: '#60A5FA',
  infoContainer: '#1E3A8A',
  onInfo: '#000000',
  onInfoContainer: '#EFF6FF',

  // Legacy colors for backward compatibility
  primaryLight: '#FF8A5C',
  primaryDark: '#E55A2B',
  accent: '#F39C12',
  accentLight: '#F5B041',
  backgroundSecondary: '#1F2937',
  text: '#F9FAFB',
  textSecondary: '#D1D5DB',
  textLight: '#9CA3AF',
  disabled: '#6B7280',
  placeholder: '#9CA3AF',
  backdrop: 'rgba(0, 0, 0, 0.7)',
  notification: '#FF8A5C',
  errorLight: '#7F1D1D',
  successLight: '#064E3B',
  warningLight: '#78350F',
  infoLight: '#1E3A8A',
  light: '#374151',
  dark: '#111827',
  border: '#4B5563',
  borderLight: '#374151',
  overlay: 'rgba(0, 0, 0, 0.3)',
};

export const theme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    ...lightColors,
  },
  dark: false,
  // Modern gradient system
  gradients: {
    primary: ['#FF6B35', '#FF8A5C'],
    primaryReverse: ['#FF8A5C', '#FF6B35'],
    primarySubtle: ['#FFE8E0', '#FFF5F3'],
    secondary: ['#6B7280', '#9CA3AF'],
    secondaryReverse: ['#9CA3AF', '#6B7280'],
    tertiary: ['#0891B2', '#22D3EE'],
    success: ['#10B981', '#34D399'],
    successSubtle: ['#ECFDF5', '#F0FDF4'],
    warning: ['#F59E0B', '#FBBF24'],
    warningSubtle: ['#FFFBEB', '#FEF3C7'],
    error: ['#EF4444', '#F87171'],
    errorSubtle: ['#FEF2F2', '#FEE2E2'],
    info: ['#3B82F6', '#60A5FA'],
    infoSubtle: ['#EFF6FF', '#DBEAFE'],
    background: ['#FAFBFC', '#FFFFFF'],
    backgroundReverse: ['#FFFFFF', '#FAFBFC'],
    card: ['#FFFFFF', '#F8FAFC'],
    cardReverse: ['#F8FAFC', '#FFFFFF'],
    surface: ['#FFFFFF', '#FAFBFC'],
    glass: ['rgba(255, 255, 255, 0.8)', 'rgba(255, 255, 255, 0.6)'],
    glassDark: ['rgba(31, 41, 55, 0.8)', 'rgba(31, 41, 55, 0.6)'],
    shimmer: ['#F3F4F6', '#E5E7EB', '#F3F4F6'],
  },
  // Enhanced font system
  fonts: {
    ...DefaultTheme.fonts,
    thin: {
      fontFamily: 'System',
      fontWeight: '100',
    },
    light: {
      fontFamily: 'System',
      fontWeight: '300',
    },
    regular: {
      fontFamily: 'System',
      fontWeight: '400',
    },
    medium: {
      fontFamily: 'System',
      fontWeight: '500',
    },
    semiBold: {
      fontFamily: 'System',
      fontWeight: '600',
    },
    bold: {
      fontFamily: 'System',
      fontWeight: '700',
    },
    extraBold: {
      fontFamily: 'System',
      fontWeight: '800',
    },
    black: {
      fontFamily: 'System',
      fontWeight: '900',
    },
  },

  // Modern typography scale
  typography: {
    // Display styles for hero sections
    displayLarge: { fontSize: 57, fontWeight: '400', lineHeight: 64, letterSpacing: -0.25 },
    displayMedium: { fontSize: 45, fontWeight: '400', lineHeight: 52, letterSpacing: 0 },
    displaySmall: { fontSize: 36, fontWeight: '400', lineHeight: 44, letterSpacing: 0 },

    // Headline styles for section headers
    headlineLarge: { fontSize: 32, fontWeight: '600', lineHeight: 40, letterSpacing: 0 },
    headlineMedium: { fontSize: 28, fontWeight: '600', lineHeight: 36, letterSpacing: 0 },
    headlineSmall: { fontSize: 24, fontWeight: '600', lineHeight: 32, letterSpacing: 0 },

    // Title styles for card headers and important text
    titleLarge: { fontSize: 22, fontWeight: '500', lineHeight: 28, letterSpacing: 0 },
    titleMedium: { fontSize: 16, fontWeight: '600', lineHeight: 24, letterSpacing: 0.15 },
    titleSmall: { fontSize: 14, fontWeight: '600', lineHeight: 20, letterSpacing: 0.1 },

    // Label styles for buttons and form labels
    labelLarge: { fontSize: 14, fontWeight: '600', lineHeight: 20, letterSpacing: 0.1 },
    labelMedium: { fontSize: 12, fontWeight: '600', lineHeight: 16, letterSpacing: 0.5 },
    labelSmall: { fontSize: 11, fontWeight: '600', lineHeight: 16, letterSpacing: 0.5 },

    // Body styles for content
    bodyLarge: { fontSize: 16, fontWeight: '400', lineHeight: 24, letterSpacing: 0.5 },
    bodyMedium: { fontSize: 14, fontWeight: '400', lineHeight: 20, letterSpacing: 0.25 },
    bodySmall: { fontSize: 12, fontWeight: '400', lineHeight: 16, letterSpacing: 0.4 },

    // Legacy typography for backward compatibility
    h1: { fontSize: 32, fontWeight: '700', lineHeight: 40 },
    h2: { fontSize: 28, fontWeight: '600', lineHeight: 36 },
    h3: { fontSize: 24, fontWeight: '600', lineHeight: 32 },
    h4: { fontSize: 20, fontWeight: '600', lineHeight: 28 },
    h5: { fontSize: 18, fontWeight: '500', lineHeight: 24 },
    h6: { fontSize: 16, fontWeight: '500', lineHeight: 22 },
    body1: { fontSize: 16, fontWeight: '400', lineHeight: 24 },
    body2: { fontSize: 14, fontWeight: '400', lineHeight: 20 },
    caption: { fontSize: 12, fontWeight: '400', lineHeight: 16 },
    overline: { fontSize: 10, fontWeight: '500', lineHeight: 14, textTransform: 'uppercase' },
    button: { fontSize: 16, fontWeight: '600', lineHeight: 20 },
  },
  roundness: 12,

  // Enhanced spacing system
  spacing: {
    none: 0,
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
    xxl: 48,
    xxxl: 64,
    xxxxl: 80,
    xxxxxl: 96,
  },

  // Modern border radius system
  borderRadius: {
    none: 0,
    xs: 2,
    sm: 4,
    md: 8,
    lg: 12,
    xl: 16,
    xxl: 24,
    xxxl: 32,
    full: 9999,
  },
  // Enhanced shadow system with modern elevation
  shadows: {
    none: {
      shadowColor: 'transparent',
      shadowOffset: { width: 0, height: 0 },
      shadowOpacity: 0,
      shadowRadius: 0,
      elevation: 0,
    },
    xs: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.05,
      shadowRadius: 2,
      elevation: 1,
    },
    small: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.08,
      shadowRadius: 4,
      elevation: 3,
    },
    medium: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.12,
      shadowRadius: 8,
      elevation: 6,
    },
    large: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 8 },
      shadowOpacity: 0.16,
      shadowRadius: 16,
      elevation: 12,
    },
    xl: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 12 },
      shadowOpacity: 0.2,
      shadowRadius: 24,
      elevation: 20,
    },
    floating: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 16 },
      shadowOpacity: 0.24,
      shadowRadius: 32,
      elevation: 24,
    },
    // Colored shadows for modern effects
    primaryShadow: {
      shadowColor: '#FF6B35',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.2,
      shadowRadius: 8,
      elevation: 6,
    },
    successShadow: {
      shadowColor: '#10B981',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.2,
      shadowRadius: 8,
      elevation: 6,
    },
    errorShadow: {
      shadowColor: '#EF4444',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.2,
      shadowRadius: 8,
      elevation: 6,
    },
  },
  // Enhanced animation system
  animations: {
    timing: {
      instant: 0,
      fast: 150,
      normal: 250,
      slow: 350,
      slower: 500,
      slowest: 750,
    },
    easing: {
      linear: 'linear',
      easeIn: 'ease-in',
      easeOut: 'ease-out',
      easeInOut: 'ease-in-out',
      // Custom easing curves
      spring: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
      smooth: 'cubic-bezier(0.4, 0, 0.2, 1)',
      sharp: 'cubic-bezier(0.4, 0, 0.6, 1)',
      emphasized: 'cubic-bezier(0.2, 0, 0, 1)',
    },
    // Spring animation configs
    spring: {
      gentle: { tension: 120, friction: 14 },
      wobbly: { tension: 180, friction: 12 },
      stiff: { tension: 210, friction: 20 },
      slow: { tension: 280, friction: 60 },
      molasses: { tension: 280, friction: 120 },
    },
  },

  // Component-specific configurations
  components: {
    card: {
      borderRadius: 12,
      padding: 16,
      shadow: 'medium',
    },
    button: {
      borderRadius: 8,
      minHeight: 44,
      paddingHorizontal: 16,
    },
    input: {
      borderRadius: 8,
      minHeight: 48,
      paddingHorizontal: 12,
    },
    chip: {
      borderRadius: 16,
      paddingHorizontal: 12,
      paddingVertical: 6,
    },
  },

  // Breakpoints for responsive design
  breakpoints: {
    xs: 0,
    sm: 576,
    md: 768,
    lg: 992,
    xl: 1200,
  },
};

// Dark theme variant
export const darkTheme = {
  ...theme,
  colors: {
    ...theme.colors,
    ...darkColors,
  },
  dark: true,
  gradients: {
    ...theme.gradients,
    // Override gradients for dark theme
    glass: ['rgba(31, 41, 55, 0.8)', 'rgba(31, 41, 55, 0.6)'],
    glassDark: ['rgba(255, 255, 255, 0.1)', 'rgba(255, 255, 255, 0.05)'],
    background: ['#111827', '#1F2937'],
    backgroundReverse: ['#1F2937', '#111827'],
    card: ['#1F2937', '#374151'],
    cardReverse: ['#374151', '#1F2937'],
    surface: ['#1F2937', '#374151'],
    shimmer: ['#374151', '#4B5563', '#374151'],
  },
};
