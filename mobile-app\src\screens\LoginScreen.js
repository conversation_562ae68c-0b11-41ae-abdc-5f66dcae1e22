import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Alert,
} from 'react-native';
import { Snackbar } from 'react-native-paper';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useAuth } from '../context/AuthContext';
import { ModernCard } from '../components/ModernCard';
import { ModernButton } from '../components/ModernButton';
import { ModernInput, EmailInput, PasswordInput } from '../components/ModernInput';
import { theme } from '../theme/theme';

export default function LoginScreen() {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  const { login } = useAuth();

  const handleLogin = async () => {
    if (!username.trim() || !password.trim()) {
      showSnackbar('Please enter both username and password');
      return;
    }

    setLoading(true);
    try {
      const result = await login(username.trim(), password);
      if (!result.success) {
        showSnackbar(result.message || 'Login failed');
      }
    } catch (error) {
      showSnackbar('An error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const showSnackbar = (message) => {
    setSnackbarMessage(message);
    setSnackbarVisible(true);
  };

  return (
    <LinearGradient
      colors={theme.gradients.primary}
      style={styles.container}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
    >
      <KeyboardAvoidingView
        style={styles.keyboardContainer}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContainer}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.logoContainer}>
            <LinearGradient
              colors={theme.gradients.glass}
              style={styles.iconContainer}
            >
              <Ionicons
                name="construct"
                size={64}
                color="white"
              />
            </LinearGradient>
            <Text style={styles.title}>Flori Construction</Text>
            <Text style={styles.subtitle}>Admin Panel</Text>
          </View>

          <ModernCard
            style={styles.card}
            elevation="xl"
            borderRadius={theme.borderRadius.xxl}
            animated={true}
            variant="elevated"
          >
            <Text style={styles.loginTitle}>Welcome Back</Text>
            <Text style={styles.loginSubtitle}>
              Sign in to manage your construction projects
            </Text>

            <EmailInput
              label="Username or Email"
              value={username}
              onChangeText={setUsername}
              placeholder="Enter your email or username"
              disabled={loading}
              style={styles.input}
            />

            <PasswordInput
              label="Password"
              value={password}
              onChangeText={setPassword}
              placeholder="Enter your password"
              disabled={loading}
              style={styles.input}
              onSubmitEditing={handleLogin}
            />

            <ModernButton
              title={loading ? 'Signing In...' : 'Sign In'}
              variant="filled"
              size="large"
              onPress={handleLogin}
              style={styles.loginButton}
              disabled={loading}
              loading={loading}
              icon="log-in-outline"
              fullWidth={true}
            />

            <View style={styles.helpContainer}>
              <Text style={styles.helpText}>
                Default credentials: admin / admin123
              </Text>
            </View>
          </ModernCard>
        </ScrollView>

        <Snackbar
          visible={snackbarVisible}
          onDismiss={() => setSnackbarVisible(false)}
          duration={4000}
          style={styles.snackbar}
        >
          {snackbarMessage}
        </Snackbar>
      </KeyboardAvoidingView>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  keyboardContainer: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: theme.spacing.lg,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: theme.spacing.xxxxl,
  },
  iconContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: theme.spacing.xl,
    ...theme.shadows.large,
  },
  title: {
    ...theme.typography.displayMedium,
    color: 'white',
    fontWeight: '700',
    textAlign: 'center',
    marginBottom: theme.spacing.xs,
  },
  subtitle: {
    ...theme.typography.titleMedium,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
  },
  card: {
    marginHorizontal: theme.spacing.md,
    padding: theme.spacing.xl,
  },
  loginTitle: {
    ...theme.typography.headlineMedium,
    color: theme.colors.onSurface,
    fontWeight: '700',
    textAlign: 'center',
    marginBottom: theme.spacing.sm,
  },
  loginSubtitle: {
    ...theme.typography.bodyLarge,
    color: theme.colors.onSurfaceVariant,
    textAlign: 'center',
    marginBottom: theme.spacing.xl,
    lineHeight: 24,
  },
  input: {
    marginBottom: theme.spacing.md,
  },
  loginButton: {
    marginTop: theme.spacing.lg,
    marginBottom: theme.spacing.xl,
  },
  helpContainer: {
    alignItems: 'center',
    paddingTop: theme.spacing.md,
    borderTopWidth: 1,
    borderTopColor: theme.colors.outline,
  },
  helpText: {
    ...theme.typography.bodySmall,
    color: theme.colors.onSurfaceVariant,
    textAlign: 'center',
  },
  snackbar: {
    backgroundColor: theme.colors.error,
  },
});
