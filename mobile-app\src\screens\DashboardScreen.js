import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
  Dimensions,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useFocusEffect } from '@react-navigation/native';
import * as SecureStore from 'expo-secure-store';
import { useAuth } from '../context/AuthContext';
import { apiService } from '../services/apiService';
import { ModernCard, StatCard } from '../components/ModernCard';
import { ModernButton } from '../components/ModernButton';
import { StatusChip } from '../components/ModernChip';
import { StatCardSkeleton, ListSkeleton, HeaderSkeleton } from '../components/ModernLoading';
import { theme } from '../theme/theme';

const { width } = Dimensions.get('window');

export default function DashboardScreen({ navigation }) {
  const [stats, setStats] = useState({});
  const [recentProjects, setRecentProjects] = useState([]);
  const [recentMessages, setRecentMessages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const { user, logout } = useAuth();

  useFocusEffect(
    useCallback(() => {
      loadDashboardData();
    }, [])
  );

  const loadDashboardData = async () => {
    try {
      setLoading(true);

      console.log('🔄 Loading dashboard data...');

      // Check if we have a token and ensure it's set in the API service
      if (user && !apiService.getAuthStatus().hasToken) {
        console.log('⚠️ User is logged in but API service has no token, attempting to restore...');
        // Try to get token from secure storage and set it
        try {
          const storedToken = await SecureStore.getItemAsync('authToken');
          if (storedToken) {
            console.log('🔑 Restoring token from secure storage');
            apiService.setAuthToken(storedToken);
          }
        } catch (error) {
          console.error('Failed to restore token:', error);
        }
      }

      // Load dashboard statistics with caching
      const dashboardResponse = await apiService.getDashboardStatsWithCache();
      console.log('📊 Dashboard response:', dashboardResponse);

      if (dashboardResponse.fromCache) {
        console.log('📦 Dashboard data loaded from cache');
      }

      if (dashboardResponse.success) {
        const data = dashboardResponse.data;

        // Set statistics
        setStats({
          totalProjects: data.projects.total,
          completedProjects: data.projects.completed,
          ongoingProjects: data.projects.ongoing,
          plannedProjects: data.projects.planned,
          newMessages: data.messages.new,
          totalMessages: data.messages.total,
          totalServices: data.services.total,
          totalMedia: data.media.total,
        });

        // Set recent data
        setRecentProjects(data.recent_projects || []);
        setRecentMessages(data.recent_messages || []);

        console.log('✅ Dashboard data loaded successfully');
      }
    } catch (error) {
      console.error('❌ Error loading dashboard data:', error);
      console.error('Error details:', {
        name: error.name,
        message: error.message,
        stack: error.stack
      });

      // Fallback to individual API calls with caching if dashboard endpoint fails
      try {
        console.log('🔄 Trying fallback API calls with cache...');
        const [projectsResponse, messagesResponse] = await Promise.all([
          apiService.getProjectsWithCache({ limit: 5 }),
          apiService.getMessagesWithCache({ status: 'new', limit: 5 }),
        ]);

        if (projectsResponse.success) {
          setRecentProjects(projectsResponse.data.projects);
          const projects = projectsResponse.data.projects;
          const completedCount = projects.filter(p => p.status === 'completed').length;
          const ongoingCount = projects.filter(p => p.status === 'ongoing').length;

          setStats({
            totalProjects: projectsResponse.data.pagination.total,
            completedProjects: completedCount,
            ongoingProjects: ongoingCount,
            newMessages: messagesResponse.success ? messagesResponse.data.pagination.total : 0,
          });

          console.log('✅ Fallback data loaded successfully');
        }

        if (messagesResponse.success) {
          setRecentMessages(messagesResponse.data.messages);
        }
      } catch (fallbackError) {
        console.error('❌ Fallback dashboard loading also failed:', fallbackError);
      }
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const testConnection = async () => {
    try {
      console.log('🧪 Testing API connection...');

      // Check auth status first
      const authStatus = apiService.getAuthStatus();
      console.log('🔑 Auth status:', authStatus);

      // Test basic connection first
      try {
        const testResponse = await apiService.testConnection();
        console.log('✅ Basic test response:', testResponse);
      } catch (testError) {
        console.log('❌ Basic test failed:', testError.message);
      }

      // Test debug endpoint
      try {
        const debugResponse = await apiService.debugConnection();
        console.log('🔍 Debug response:', debugResponse);

        if (debugResponse.success) {
          const data = debugResponse.data;
          alert(`Debug Info:
- Database: ${data.database_connection}
- Auth Token in Request: ${data.auth_token_found ? 'Found' : 'Missing'}
- Auth Token in App: ${authStatus.hasToken ? 'Found' : 'Missing'}
- Token Verification: ${data.token_verification || 'Not tested'}
- User Count: ${data.user_count || 'Unknown'}
- User Info: ${user ? user.username : 'Not logged in'}

Check console for full details.`);
        }
      } catch (debugError) {
        console.log('❌ Debug test failed:', debugError.message);
        alert('Debug test failed: ' + debugError.message);
      }

    } catch (error) {
      console.error('❌ Connection test failed:', error);
      alert('Connection test failed: ' + error.message);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadDashboardData();
  };

  const handleLogout = async () => {
    await logout();
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return theme.colors.success;
      case 'ongoing':
        return theme.colors.warning;
      case 'planned':
        return theme.colors.info;
      default:
        return theme.colors.placeholder;
    }
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <HeaderSkeleton />
        <View style={styles.statsContainer}>
          <View style={styles.statsRow}>
            <StatCardSkeleton style={styles.statCard} />
            <StatCardSkeleton style={styles.statCard} />
          </View>
          <View style={styles.statsRow}>
            <StatCardSkeleton style={styles.statCard} />
            <StatCardSkeleton style={styles.statCard} />
          </View>
        </View>
        <ListSkeleton count={3} />
      </View>
    );
  }

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      {/* Header */}
      <LinearGradient
        colors={theme.gradients.primary}
        style={styles.header}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <View style={styles.headerContent}>
          <View style={styles.headerTextContainer}>
            <Text style={styles.welcomeTitle}>Welcome back,</Text>
            <Text style={styles.userName}>{user?.username || 'User'}</Text>
            <Text style={styles.headerSubtitle}>
              {new Date().toLocaleDateString('en-US', {
                weekday: 'long',
                month: 'long',
                day: 'numeric'
              })}
            </Text>
          </View>
          <ModernButton
            icon="log-out-outline"
            variant="tonal"
            size="medium"
            onPress={handleLogout}
            style={styles.logoutButton}
          />
        </View>
      </LinearGradient>

      {/* Statistics Cards */}
      <View style={styles.statsContainer}>
        <View style={styles.statsRow}>
          <StatCard
            gradient={true}
            gradientColors={theme.gradients.primary}
            style={styles.statCard}
            elevation="medium"
            animated={true}
          >
            <View style={styles.statCardHeader}>
              <View style={styles.statIconContainer}>
                <Ionicons name="business-outline" size={32} color="white" />
              </View>
              <Text style={styles.statNumber}>{stats.totalProjects || 0}</Text>
              <Text style={styles.statLabel}>Total Projects</Text>
            </View>
          </StatCard>

          <StatCard
            gradient={true}
            gradientColors={theme.gradients.success}
            style={styles.statCard}
            elevation="medium"
            animated={true}
          >
            <View style={styles.statCardHeader}>
              <View style={styles.statIconContainer}>
                <Ionicons name="checkmark-circle-outline" size={32} color="white" />
              </View>
              <Text style={styles.statNumber}>{stats.completedProjects || 0}</Text>
              <Text style={styles.statLabel}>Completed</Text>
            </View>
          </StatCard>
        </View>

        <View style={styles.statsRow}>
          <StatCard
            gradient={true}
            gradientColors={theme.gradients.warning}
            style={styles.statCard}
            elevation="medium"
            animated={true}
          >
            <View style={styles.statCardHeader}>
              <View style={styles.statIconContainer}>
                <Ionicons name="construct-outline" size={32} color="white" />
              </View>
              <Text style={styles.statNumber}>{stats.ongoingProjects || 0}</Text>
              <Text style={styles.statLabel}>Ongoing</Text>
            </View>
          </StatCard>

          <StatCard
            gradient={true}
            gradientColors={theme.gradients.info}
            style={styles.statCard}
            elevation="medium"
            animated={true}
          >
            <View style={styles.statCardHeader}>
              <View style={styles.statIconContainer}>
                <Ionicons name="mail-outline" size={32} color="white" />
              </View>
              <Text style={styles.statNumber}>{stats.newMessages || 0}</Text>
              <Text style={styles.statLabel}>New Messages</Text>
            </View>
          </StatCard>
        </View>
      </View>

      {/* Recent Projects */}
      <ModernCard style={styles.sectionCard} elevation="medium" animated={true}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Recent Projects</Text>
          <ModernButton
            title="View All"
            variant="text"
            size="small"
            onPress={() => navigation.navigate('Projects')}
            icon="arrow-forward-outline"
            iconPosition="right"
          />
        </View>

        {recentProjects.length > 0 ? (
          recentProjects.map((project, index) => (
            <TouchableOpacity
              key={project.id}
              style={[styles.listItem, index === recentProjects.length - 1 && styles.lastListItem]}
              onPress={() => navigation.navigate('ProjectDetail', { project })}
            >
              <View style={styles.listItemLeft}>
                <View style={[styles.projectIcon, { backgroundColor: theme.colors.primaryContainer }]}>
                  <Ionicons name="business-outline" size={20} color={theme.colors.primary} />
                </View>
                <View style={styles.listItemContent}>
                  <Text style={styles.listItemTitle}>{project.title}</Text>
                  <Text style={styles.listItemDescription}>{project.location}</Text>
                </View>
              </View>
              <StatusChip status={project.status} />
            </TouchableOpacity>
          ))
        ) : (
          <View style={styles.emptyState}>
            <Ionicons name="business-outline" size={48} color={theme.colors.onSurfaceVariant} />
            <Text style={styles.emptyText}>No recent projects</Text>
          </View>
        )}
      </ModernCard>

      {/* Recent Messages */}
      <ModernCard style={styles.sectionCard} elevation="medium" animated={true}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>New Messages</Text>
          <ModernButton
            title="View All"
            variant="text"
            size="small"
            onPress={() => navigation.navigate('Messages')}
            icon="arrow-forward-outline"
            iconPosition="right"
          />
        </View>

        {recentMessages.length > 0 ? (
          recentMessages.map((message, index) => (
            <TouchableOpacity
              key={message.id}
              style={[styles.listItem, index === recentMessages.length - 1 && styles.lastListItem]}
              onPress={() => navigation.navigate('MessageDetail', { message })}
            >
              <View style={styles.listItemLeft}>
                <View style={[styles.messageIcon, { backgroundColor: theme.colors.tertiaryContainer }]}>
                  <Ionicons name="mail-outline" size={20} color={theme.colors.tertiary} />
                </View>
                <View style={styles.listItemContent}>
                  <Text style={styles.listItemTitle}>{message.name}</Text>
                  <Text style={styles.listItemDescription}>
                    {message.subject || 'No subject'}
                  </Text>
                </View>
              </View>
              <Text style={styles.messageTime}>
                {new Date(message.created_at).toLocaleDateString()}
              </Text>
            </TouchableOpacity>
          ))
        ) : (
          <View style={styles.emptyState}>
            <Ionicons name="mail-outline" size={48} color={theme.colors.onSurfaceVariant} />
            <Text style={styles.emptyText}>No new messages</Text>
          </View>
        )}
      </ModernCard>

      {/* Quick Actions */}
      <ModernCard style={styles.sectionCard} elevation="medium" animated={true}>
        <Text style={styles.sectionTitle}>Quick Actions</Text>
        <View style={styles.quickActions}>
          <ModernButton
            title="Add Project"
            variant="filled"
            size="large"
            onPress={() => navigation.navigate('AddProject')}
            icon="add-outline"
            style={styles.quickActionButton}
            fullWidth={true}
          />
          <ModernButton
            title="Add Service"
            variant="tonal"
            size="large"
            onPress={() => navigation.navigate('AddService')}
            icon="add-outline"
            style={styles.quickActionButton}
            fullWidth={true}
          />
        </View>

        {/* Debug button - remove in production */}
        <View style={styles.debugActions}>
          <ModernButton
            title="Test API Connection"
            variant="outlined"
            size="small"
            onPress={testConnection}
            icon="bug-outline"
            style={styles.debugButton}
            fullWidth={true}
          />
        </View>
      </ModernCard>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    paddingTop: theme.spacing.xl,
    paddingBottom: theme.spacing.xl,
    paddingHorizontal: theme.spacing.md,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTextContainer: {
    flex: 1,
  },
  welcomeTitle: {
    ...theme.typography.headlineMedium,
    color: 'white',
    fontWeight: '700',
  },
  userName: {
    ...theme.typography.titleLarge,
    color: 'rgba(255, 255, 255, 0.9)',
    marginTop: theme.spacing.xs,
  },
  headerSubtitle: {
    ...theme.typography.bodyMedium,
    color: 'rgba(255, 255, 255, 0.7)',
    marginTop: theme.spacing.xs,
  },
  logoutButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  statsContainer: {
    padding: theme.spacing.md,
    marginTop: -theme.spacing.lg,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: theme.spacing.md,
  },
  statCard: {
    flex: 1,
    marginHorizontal: theme.spacing.xs,
    minHeight: 120,
  },
  statCardHeader: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  statIconContainer: {
    marginBottom: theme.spacing.sm,
  },
  statNumber: {
    ...theme.typography.displaySmall,
    color: 'white',
    fontWeight: '700',
    marginBottom: theme.spacing.xs,
  },
  statLabel: {
    ...theme.typography.labelMedium,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    fontWeight: '600',
  },
  sectionCard: {
    margin: theme.spacing.md,
    marginTop: 0,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.lg,
  },
  sectionTitle: {
    ...theme.typography.titleLarge,
    color: theme.colors.onSurface,
    fontWeight: '600',
  },
  listItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: theme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.outline,
  },
  lastListItem: {
    borderBottomWidth: 0,
  },
  listItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  listItemContent: {
    flex: 1,
    marginLeft: theme.spacing.md,
  },
  listItemTitle: {
    ...theme.typography.bodyLarge,
    color: theme.colors.onSurface,
    fontWeight: '500',
    marginBottom: theme.spacing.xs,
  },
  listItemDescription: {
    ...theme.typography.bodyMedium,
    color: theme.colors.onSurfaceVariant,
  },
  projectIcon: {
    width: 40,
    height: 40,
    borderRadius: theme.borderRadius.md,
    justifyContent: 'center',
    alignItems: 'center',
  },
  messageIcon: {
    width: 40,
    height: 40,
    borderRadius: theme.borderRadius.md,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: theme.spacing.xl,
  },
  emptyText: {
    ...theme.typography.bodyLarge,
    color: theme.colors.onSurfaceVariant,
    textAlign: 'center',
    marginTop: theme.spacing.md,
  },
  messageTime: {
    ...theme.typography.labelSmall,
    color: theme.colors.onSurfaceVariant,
  },
  quickActions: {
    gap: theme.spacing.md,
    marginTop: theme.spacing.md,
  },
  quickActionButton: {
    marginBottom: theme.spacing.sm,
  },
  debugActions: {
    marginTop: theme.spacing.lg,
    paddingTop: theme.spacing.md,
    borderTopWidth: 1,
    borderTopColor: theme.colors.disabled,
  },
  debugButton: {
    borderColor: theme.colors.warning,
  },
  headerSkeleton: {
    paddingTop: theme.spacing.lg,
    paddingBottom: theme.spacing.xl,
    paddingHorizontal: theme.spacing.md,
  },
  skeletonText: {
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: theme.borderRadius.sm,
  },
  skeletonButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: theme.borderRadius.md,
  },
  centerLoader: {
    marginTop: theme.spacing.xxxl,
  },
});
