import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  Alert,
  StatusBar,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { apiService } from '../services/apiService';
import { AdvancedSearchBar } from '../components/SearchBar';
import { ModernCard, ProjectCard } from '../components/ModernCard';
import { ModernButton, FAB } from '../components/ModernButton';
import { ProjectCardSkeleton, ListSkeleton, HeaderSkeleton, SearchBarSkeleton } from '../components/ModernLoading';
import { theme } from '../theme/theme';
import { useAuth } from '../context/AuthContext';

export default function ProjectsScreen({ navigation }) {
  const { loading: authLoading, isAuthenticated, token, user } = useAuth();
  const [projects, setProjects] = useState([]);
  const [filteredProjects, setFilteredProjects] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilters, setSelectedFilters] = useState([]);

  useEffect(() => {
    // Only load projects when authentication is complete and user is authenticated
    if (!authLoading && isAuthenticated && token) {
      console.log('🔄 Auth state ready, loading projects...');
      // Add a small delay to ensure token is properly set
      const timer = setTimeout(() => {
        loadProjects();
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [authLoading, isAuthenticated, token]);

  useEffect(() => {
    filterProjects();
  }, [projects, searchQuery, selectedFilters]);

  const filterProjects = () => {
    let filtered = [...projects];

    // Apply text search
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(project =>
        project.title.toLowerCase().includes(query) ||
        project.description.toLowerCase().includes(query) ||
        project.location.toLowerCase().includes(query) ||
        (project.client && project.client.toLowerCase().includes(query))
      );
    }

    // Apply status filters
    if (selectedFilters.length > 0) {
      filtered = filtered.filter(project =>
        selectedFilters.includes(project.status)
      );
    }

    setFilteredProjects(filtered);
  };

  const handleSearch = (query, filters) => {
    setSearchQuery(query);
    setSelectedFilters(filters);
  };

  const loadProjects = async () => {
    try {
      setLoading(true);
      console.log('🔄 Loading projects...');

      // Check if we have a token and ensure it's set in the API service
      if (user && !apiService.getAuthStatus().hasToken && token) {
        console.log('⚠️ User is logged in but API service has no token, attempting to restore...');
        console.log('🔑 Restoring token from AuthContext for projects');
        console.log('🔍 Token preview:', token.substring(0, 50) + '...');
        apiService.setAuthToken(token);
      }

      // Debug: Check current auth status before API call
      const authStatus = apiService.getAuthStatus();
      console.log('🔍 Auth status before projects API call:', authStatus);
      console.log('🔍 Full token being used:', authStatus.tokenPreview ? authStatus.tokenPreview.substring(0, 100) + '...' : 'null');

      const response = await apiService.getProjectsWithCache();
      if (response.success) {
        console.log('✅ Projects loaded successfully:', response.data.projects?.length || response.data?.length || 0, 'projects');
        setProjects(response.data.projects || response.data);
      } else {
        console.error('❌ Failed to load projects:', response.message);
        Alert.alert('Error', 'Failed to load projects');
      }
    } catch (error) {
      console.error('Error loading projects:', error);
      Alert.alert('Error', 'Failed to load projects');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadProjects();
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return theme.colors.success;
      case 'ongoing':
        return theme.colors.warning;
      case 'planned':
        return theme.colors.info;
      default:
        return theme.colors.placeholder;
    }
  };

  const renderProject = ({ item }) => (
    <ProjectCard
      style={styles.projectCard}
      onPress={() => navigation.navigate('ProjectDetail', { project: item })}
      elevation="medium"
      animated={true}
      pressScale={0.98}
    >
      <View style={styles.projectHeader}>
        <View style={styles.projectTitleContainer}>
          <Text style={styles.projectTitle}>{item.title}</Text>
          <Text style={styles.projectSubtitle} numberOfLines={1}>
            {item.client || 'No client specified'}
          </Text>
        </View>
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}>
          <Text style={styles.statusText}>{item.status}</Text>
        </View>
      </View>

      <View style={styles.projectInfo}>
        <View style={styles.projectInfoRow}>
          <View style={[styles.iconContainer, { backgroundColor: theme.colors.primaryContainer }]}>
            <Ionicons name="location-outline" size={16} color={theme.colors.primary} />
          </View>
          <Text style={styles.projectLocation}>{item.location}</Text>
        </View>

        {item.start_date && (
          <View style={styles.projectInfoRow}>
            <View style={[styles.iconContainer, { backgroundColor: theme.colors.tertiaryContainer }]}>
              <Ionicons name="calendar-outline" size={16} color={theme.colors.tertiary} />
            </View>
            <Text style={styles.projectDate}>
              Started {new Date(item.start_date).toLocaleDateString()}
            </Text>
          </View>
        )}
      </View>

      <Text style={styles.projectDescription} numberOfLines={2}>
        {item.description}
      </Text>

      <View style={styles.projectFooter}>
        <View style={styles.projectProgress}>
          <View style={[styles.progressBar, { backgroundColor: theme.colors.outline }]}>
            <View
              style={[
                styles.progressFill,
                {
                  backgroundColor: getStatusColor(item.status),
                  width: item.status === 'completed' ? '100%' :
                    item.status === 'ongoing' ? '60%' : '20%'
                }
              ]}
            />
          </View>
          <Text style={styles.progressText}>
            {item.status === 'completed' ? '100%' :
              item.status === 'ongoing' ? '60%' : '20%'} complete
          </Text>
        </View>
        <TouchableOpacity
          style={styles.viewButton}
          onPress={() => navigation.navigate('ProjectDetail', { project: item })}
        >
          <Ionicons name="arrow-forward" size={16} color={theme.colors.primary} />
        </TouchableOpacity>
      </View>
    </ProjectCard>
  );

  // Show loading while authentication is in progress
  if (authLoading) {
    return (
      <View style={styles.centerContainer}>
        <Text>Authenticating...</Text>
      </View>
    );
  }

  // Show loading while projects are being fetched
  if (loading && !refreshing) {
    return (
      <View style={styles.container}>
        <HeaderSkeleton />
        <SearchBarSkeleton />
        <ListSkeleton count={6} />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={theme.gradients.primary}
        style={styles.header}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <View style={styles.headerContent}>
          <View style={styles.headerTextContainer}>
            <Text style={styles.headerTitle}>Projects</Text>
            <Text style={styles.headerSubtitle}>
              {filteredProjects.length} {filteredProjects.length === 1 ? 'project' : 'projects'}
            </Text>
          </View>
          <ModernButton
            icon="add"
            variant="tonal"
            size="medium"
            onPress={() => navigation.navigate('AddProject')}
            style={styles.addButton}
          />
        </View>
      </LinearGradient>

      {/* Search and Filter */}
      <View style={styles.searchContainer}>
        <AdvancedSearchBar
          placeholder="Search projects..."
          onSearch={handleSearch}
          filters={[
            { label: 'Planned', value: 'planned', icon: 'time-outline' },
            { label: 'Ongoing', value: 'ongoing', icon: 'play-outline' },
            { label: 'Completed', value: 'completed', icon: 'checkmark-outline' },
          ]}
          selectedFilters={selectedFilters}
        />
      </View>

      <FlatList
        data={filteredProjects}
        renderItem={renderProject}
        keyExtractor={(item) => item.id.toString()}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <View style={styles.emptyIconContainer}>
              <LinearGradient
                colors={theme.gradients.primarySubtle}
                style={styles.emptyIconGradient}
              >
                <Ionicons name="business-outline" size={64} color={theme.colors.primary} />
              </LinearGradient>
            </View>
            <Text style={styles.emptyText}>No projects found</Text>
            <Text style={styles.emptySubtext}>
              {searchQuery || selectedFilters.length > 0
                ? 'Try adjusting your search or filters'
                : 'Start by creating your first construction project'
              }
            </Text>
            {(!searchQuery && selectedFilters.length === 0) && (
              <ModernButton
                title="Add First Project"
                variant="filled"
                size="large"
                onPress={() => navigation.navigate('AddProject')}
                icon="add-outline"
                style={styles.emptyButton}
              />
            )}
          </View>
        }
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    paddingTop: theme.spacing.xl,
    paddingBottom: theme.spacing.lg,
    paddingHorizontal: theme.spacing.md,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTextContainer: {
    flex: 1,
  },
  headerTitle: {
    ...theme.typography.headlineMedium,
    color: 'white',
    fontWeight: '700',
  },
  headerSubtitle: {
    ...theme.typography.bodyMedium,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: theme.spacing.xs,
  },
  addButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  searchContainer: {
    backgroundColor: theme.colors.surface,
    paddingHorizontal: theme.spacing.md,
    paddingBottom: theme.spacing.md,
    ...theme.shadows.xs,
  },
  listContainer: {
    padding: theme.spacing.md,
  },
  projectCard: {
    marginBottom: theme.spacing.lg,
    marginHorizontal: theme.spacing.xs,
  },
  projectHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: theme.spacing.md,
  },
  projectTitleContainer: {
    flex: 1,
    marginRight: theme.spacing.md,
  },
  projectTitle: {
    ...theme.typography.titleLarge,
    color: theme.colors.onSurface,
    fontWeight: '600',
    marginBottom: theme.spacing.xs,
  },
  projectSubtitle: {
    ...theme.typography.bodySmall,
    color: theme.colors.onSurfaceVariant,
  },
  projectInfo: {
    marginBottom: theme.spacing.md,
  },
  projectInfoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.sm,
  },
  iconContainer: {
    width: 28,
    height: 28,
    borderRadius: theme.borderRadius.sm,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: theme.spacing.sm,
  },
  statusBadge: {
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.borderRadius.full,
    minWidth: 80,
    alignItems: 'center',
  },
  statusText: {
    ...theme.typography.labelSmall,
    color: 'white',
    fontWeight: '600',
    textTransform: 'capitalize',
  },
  projectLocation: {
    ...theme.typography.bodyMedium,
    color: theme.colors.onSurfaceVariant,
    flex: 1,
  },
  projectDate: {
    ...theme.typography.bodyMedium,
    color: theme.colors.onSurfaceVariant,
    flex: 1,
  },
  projectDescription: {
    ...theme.typography.bodyMedium,
    color: theme.colors.onSurface,
    lineHeight: 22,
    marginBottom: theme.spacing.md,
  },
  projectFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: theme.spacing.sm,
  },
  projectProgress: {
    flex: 1,
    marginRight: theme.spacing.md,
  },
  progressBar: {
    height: 4,
    borderRadius: theme.borderRadius.sm,
    marginBottom: theme.spacing.xs,
  },
  progressFill: {
    height: '100%',
    borderRadius: theme.borderRadius.sm,
  },
  progressText: {
    ...theme.typography.labelSmall,
    color: theme.colors.onSurfaceVariant,
  },
  viewButton: {
    width: 32,
    height: 32,
    borderRadius: theme.borderRadius.full,
    backgroundColor: theme.colors.primaryContainer,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: theme.spacing.xxxxl,
    paddingHorizontal: theme.spacing.xl,
  },
  emptyIconContainer: {
    marginBottom: theme.spacing.xl,
  },
  emptyIconGradient: {
    width: 120,
    height: 120,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
    ...theme.shadows.medium,
  },
  emptyText: {
    ...theme.typography.headlineSmall,
    color: theme.colors.onSurface,
    textAlign: 'center',
    marginBottom: theme.spacing.sm,
  },
  emptySubtext: {
    ...theme.typography.bodyLarge,
    color: theme.colors.onSurfaceVariant,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: theme.spacing.xl,
    maxWidth: 280,
  },
  emptyButton: {
    marginTop: theme.spacing.md,
    minWidth: 200,
  },
  skeletonSearch: {
    backgroundColor: theme.colors.surfaceVariant,
    borderRadius: theme.borderRadius.lg,
    marginHorizontal: theme.spacing.md,
  },
});
