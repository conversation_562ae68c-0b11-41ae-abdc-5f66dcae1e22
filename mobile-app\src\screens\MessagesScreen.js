import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  Alert,
  Linking,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { apiService } from '../services/apiService';
import { ModernCard } from '../components/ModernCard';
import { FilterChip, StatusChip } from '../components/ModernChip';
import { ListSkeleton, HeaderSkeleton } from '../components/ModernLoading';
import { theme } from '../theme/theme';

export default function MessagesScreen({ navigation }) {
  const [messages, setMessages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [filter, setFilter] = useState('all');

  useEffect(() => {
    loadMessages();
  }, [filter]);

  const loadMessages = async () => {
    try {
      setLoading(true);
      const statusFilter = filter === 'all' ? null : filter;
      const response = await apiService.getMessages({ status: statusFilter });
      if (response.success) {
        setMessages(response.data.messages);
      } else {
        Alert.alert('Error', 'Failed to load messages');
      }
    } catch (error) {
      console.error('Error loading messages:', error);
      Alert.alert('Error', 'Failed to load messages');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadMessages();
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'new':
        return theme.colors.warning;
      case 'read':
        return theme.colors.info;
      case 'replied':
        return theme.colors.success;
      default:
        return theme.colors.placeholder;
    }
  };

  const handleCall = (phone) => {
    if (phone) {
      Linking.openURL(`tel:${phone}`);
    }
  };

  const handleEmail = (email, subject) => {
    const emailUrl = `mailto:${email}?subject=Re: ${encodeURIComponent(subject || 'Your Inquiry')}`;
    Linking.openURL(emailUrl);
  };

  const markAsRead = async (messageId) => {
    try {
      await apiService.updateMessage(messageId, { status: 'read' });
      loadMessages();
    } catch (error) {
      console.error('Error marking message as read:', error);
    }
  };

  const renderMessage = ({ item }) => (
    <ModernCard
      style={[
        styles.messageCard,
        item.status === 'new' && styles.newMessageCard
      ]}
      onPress={() => navigation.navigate('MessageDetail', { message: item })}
      elevation="medium"
      animated={true}
      pressScale={0.98}
    >
      <View style={styles.messageHeader}>
        <View style={styles.messageInfo}>
          <View style={styles.contactInfo}>
            <View style={[styles.avatarContainer, { backgroundColor: theme.colors.primaryContainer }]}>
              <Ionicons name="person" size={20} color={theme.colors.primary} />
            </View>
            <View style={styles.contactDetails}>
              <Text style={styles.messageName}>{item.name}</Text>
              <Text style={styles.messageEmail}>{item.email}</Text>
            </View>
          </View>
        </View>
        <StatusChip
          status={item.status === 'new' ? 'planned' : item.status === 'replied' ? 'completed' : 'ongoing'}
        />
      </View>

      <Text style={styles.messageSubject}>
        {item.subject || 'No Subject'}
      </Text>

      <Text style={styles.messagePreview} numberOfLines={2}>
        {item.message}
      </Text>

      <View style={styles.messageFooter}>
        <View style={styles.messageMetadata}>
          <View style={styles.dateContainer}>
            <Ionicons name="time-outline" size={14} color={theme.colors.onSurfaceVariant} />
            <Text style={styles.messageDate}>
              {new Date(item.created_at).toLocaleDateString()}
            </Text>
          </View>
          {item.phone && (
            <View style={styles.phoneContainer}>
              <Ionicons name="call-outline" size={14} color={theme.colors.onSurfaceVariant} />
              <Text style={styles.phoneText}>{item.phone}</Text>
            </View>
          )}
        </View>
        <View style={styles.messageActions}>
          {item.phone && (
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => handleCall(item.phone)}
            >
              <Ionicons name="call" size={18} color={theme.colors.primary} />
            </TouchableOpacity>
          )}
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => handleEmail(item.email, item.subject)}
          >
            <Ionicons name="mail" size={18} color={theme.colors.primary} />
          </TouchableOpacity>
          {item.status === 'new' && (
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => markAsRead(item.id)}
            >
              <Ionicons name="eye" size={18} color={theme.colors.tertiary} />
            </TouchableOpacity>
          )}
        </View>
      </View>
    </ModernCard>
  );

  const filterButtons = [
    { key: 'all', label: 'All' },
    { key: 'new', label: 'New' },
    { key: 'read', label: 'Read' },
    { key: 'replied', label: 'Replied' },
  ];

  if (loading && !refreshing) {
    return (
      <View style={styles.container}>
        <HeaderSkeleton />
        <ListSkeleton count={5} />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={theme.gradients.primary}
        style={styles.header}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <View style={styles.headerContent}>
          <View style={styles.headerTextContainer}>
            <Text style={styles.headerTitle}>Messages</Text>
            <Text style={styles.headerSubtitle}>
              {messages.length} {messages.length === 1 ? 'message' : 'messages'}
            </Text>
          </View>
          <View style={styles.headerIcon}>
            <Ionicons name="mail-outline" size={32} color="white" />
          </View>
        </View>
      </LinearGradient>

      {/* Filter Chips */}
      <View style={styles.filterContainer}>
        {filterButtons.map((button) => (
          <FilterChip
            key={button.key}
            label={button.label}
            selected={filter === button.key}
            onToggle={() => setFilter(button.key)}
            style={styles.filterChip}
          />
        ))}
      </View>

      <FlatList
        data={messages}
        renderItem={renderMessage}
        keyExtractor={(item) => item.id.toString()}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <View style={styles.emptyIconContainer}>
              <LinearGradient
                colors={theme.gradients.primarySubtle}
                style={styles.emptyIconGradient}
              >
                <Ionicons name="mail-outline" size={64} color={theme.colors.primary} />
              </LinearGradient>
            </View>
            <Text style={styles.emptyText}>
              {filter === 'all' ? 'No messages found' : `No ${filter} messages`}
            </Text>
            <Text style={styles.emptySubtext}>
              {filter === 'all'
                ? 'Customer messages will appear here when they contact you'
                : `No messages with ${filter} status found`
              }
            </Text>
          </View>
        }
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    paddingTop: theme.spacing.xl,
    paddingBottom: theme.spacing.lg,
    paddingHorizontal: theme.spacing.md,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTextContainer: {
    flex: 1,
  },
  headerTitle: {
    ...theme.typography.headlineMedium,
    color: 'white',
    fontWeight: '700',
  },
  headerSubtitle: {
    ...theme.typography.bodyMedium,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: theme.spacing.xs,
  },
  headerIcon: {
    width: 56,
    height: 56,
    borderRadius: theme.borderRadius.lg,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  filterContainer: {
    flexDirection: 'row',
    padding: theme.spacing.md,
    backgroundColor: theme.colors.surface,
    ...theme.shadows.xs,
  },
  filterChip: {
    marginRight: theme.spacing.sm,
  },
  listContainer: {
    padding: theme.spacing.md,
  },
  messageCard: {
    marginBottom: theme.spacing.lg,
    marginHorizontal: theme.spacing.xs,
  },
  newMessageCard: {
    borderLeftWidth: 4,
    borderLeftColor: theme.colors.warning,
  },
  messageHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  messageInfo: {
    flex: 1,
  },
  contactInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatarContainer: {
    width: 40,
    height: 40,
    borderRadius: theme.borderRadius.md,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: theme.spacing.md,
  },
  contactDetails: {
    flex: 1,
  },
  messageName: {
    ...theme.typography.titleMedium,
    color: theme.colors.onSurface,
    fontWeight: '600',
    marginBottom: theme.spacing.xs,
  },
  messageEmail: {
    ...theme.typography.bodySmall,
    color: theme.colors.onSurfaceVariant,
  },
  messageSubject: {
    ...theme.typography.titleMedium,
    color: theme.colors.onSurface,
    fontWeight: '600',
    marginBottom: theme.spacing.sm,
  },
  messagePreview: {
    ...theme.typography.bodyMedium,
    color: theme.colors.onSurface,
    lineHeight: 22,
    marginBottom: theme.spacing.md,
  },
  messageFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  messageMetadata: {
    flex: 1,
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.xs,
  },
  messageDate: {
    ...theme.typography.labelSmall,
    color: theme.colors.onSurfaceVariant,
    marginLeft: theme.spacing.xs,
  },
  phoneContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  phoneText: {
    ...theme.typography.labelSmall,
    color: theme.colors.onSurfaceVariant,
    marginLeft: theme.spacing.xs,
  },
  messageActions: {
    flexDirection: 'row',
    gap: theme.spacing.xs,
  },
  actionButton: {
    width: 36,
    height: 36,
    borderRadius: theme.borderRadius.md,
    backgroundColor: theme.colors.primaryContainer,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: theme.spacing.xxxxl,
    paddingHorizontal: theme.spacing.xl,
  },
  emptyIconContainer: {
    marginBottom: theme.spacing.xl,
  },
  emptyIconGradient: {
    width: 120,
    height: 120,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
    ...theme.shadows.medium,
  },
  emptyText: {
    ...theme.typography.headlineSmall,
    color: theme.colors.onSurface,
    textAlign: 'center',
    marginBottom: theme.spacing.sm,
  },
  emptySubtext: {
    ...theme.typography.bodyLarge,
    color: theme.colors.onSurfaceVariant,
    textAlign: 'center',
    lineHeight: 24,
    maxWidth: 280,
  },
});
