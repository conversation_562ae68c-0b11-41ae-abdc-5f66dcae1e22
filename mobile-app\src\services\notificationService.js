import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import Constants from 'expo-constants';
import { Platform } from 'react-native';
import * as SecureStore from 'expo-secure-store';
import { config } from '../config/environment';

// Configure notification behavior
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
  }),
});

class NotificationService {
  constructor() {
    this.expoPushToken = null;
    this.notificationListener = null;
    this.responseListener = null;
    this.isInitialized = false;
  }

  async initialize() {
    if (this.isInitialized) return;

    try {
      if (!Device.isDevice) {
        console.log('📱 Notifications: Must use physical device for Push Notifications');
        this.isInitialized = true; // Mark as initialized to prevent retries
        return false;
      }

      // Request permissions with Android-specific error handling
      let finalStatus = 'denied';

      try {
        const { status: existingStatus } = await Notifications.getPermissionsAsync();
        finalStatus = existingStatus;

        if (existingStatus !== 'granted') {
          try {
            const { status } = await Notifications.requestPermissionsAsync();
            finalStatus = status;
          } catch (permissionError) {
            // Handle specific Android permission errors
            if (permissionError.message.includes('String resource ID #0xffffffff') ||
              permissionError.message.includes('ExpoNotificationPermissionsModule')) {
              console.log('📱 Notifications: Android permission dialog issue detected (common in development)');
              console.log('📱 Notifications: This is a known Expo development issue and won\'t affect production builds');
              console.log('📱 Notifications: App will continue without push notifications');
            } else {
              console.warn('📱 Notifications: Permission request failed:', permissionError.message);
            }
            finalStatus = 'denied';
          }
        }
      } catch (permissionError) {
        if (permissionError.message.includes('String resource ID #0xffffffff')) {
          console.log('📱 Notifications: Android resource issue detected (development only)');
        } else {
          console.warn('📱 Notifications: Permission check failed:', permissionError.message);
        }
        finalStatus = 'denied';
      }

      if (finalStatus !== 'granted') {
        console.log('📱 Notifications: Push notification permissions not granted, continuing without notifications');

        // Still set up local notification capabilities even without push permissions
        if (Platform.OS === 'android') {
          try {
            await this.setupLocalNotificationChannel();
            console.log('📱 Notifications: Local notification channel configured');
          } catch (channelError) {
            console.log('📱 Notifications: Could not set up local notification channel');
          }
        }

        this.isInitialized = true; // Mark as initialized to prevent retries
        return false;
      }

      // Get push token
      this.expoPushToken = await this.getExpoPushToken();

      if (this.expoPushToken) {
        try {
          await SecureStore.setItemAsync('expoPushToken', this.expoPushToken);
          if (config.isDevelopment) {
            console.log('📱 Push token saved:', this.expoPushToken.substring(0, 20) + '...');
          }
        } catch (tokenError) {
          console.warn('📱 Notifications: Failed to save push token:', tokenError.message);
        }
      }

      // Set up notification listeners
      this.setupNotificationListeners();

      // Configure notification channel for Android
      if (Platform.OS === 'android') {
        await this.setupLocalNotificationChannel();
      }

      this.isInitialized = true;
      console.log('📱 Notifications: Successfully initialized');
      return true;
    } catch (error) {
      console.error('📱 Notifications: Error initializing notifications:', error);
      this.isInitialized = true; // Mark as initialized to prevent retries
      return false;
    }
  }

  async getExpoPushToken() {
    try {
      if (!Device.isDevice) {
        console.log('📱 Push Token: Must use physical device for Push Notifications');
        return null;
      }

      // Get projectId from EAS configuration
      let projectId = Constants.expoConfig?.extra?.eas?.projectId ||
        Constants.expoConfig?.projectId ||
        Constants.easConfig?.projectId;

      if (!projectId) {
        console.warn('📱 Push Token: No EAS projectId found. Push notifications will not work.');
        console.warn('📱 Push Token: Run "npx eas init" to set up EAS project');
        return null;
      }

      console.log('📱 Push Token: Requesting token with projectId:', projectId.substring(0, 8) + '...');

      const token = (await Notifications.getExpoPushTokenAsync({
        projectId: projectId,
      })).data;

      if (token) {
        console.log('📱 Push Token: Successfully obtained token');
      }

      return token;
    } catch (error) {
      console.error('📱 Push Token: Error getting push token:', error);
      return null;
    }
  }

  setupNotificationListeners() {
    // Listener for notifications received while app is foregrounded
    this.notificationListener = Notifications.addNotificationReceivedListener(notification => {
      if (config.isDevelopment) {
        console.log('📨 Notification received:', notification);
      }
      this.handleNotificationReceived(notification);
    });

    // Listener for when user taps on notification
    this.responseListener = Notifications.addNotificationResponseReceivedListener(response => {
      if (config.isDevelopment) {
        console.log('👆 Notification tapped:', response);
      }
      this.handleNotificationResponse(response);
    });
  }

  handleNotificationReceived(notification) {
    const { title, body, data } = notification.request.content;

    // Handle different notification types
    switch (data?.type) {
      case 'new_message':
        this.handleNewMessageNotification(data);
        break;
      case 'project_update':
        this.handleProjectUpdateNotification(data);
        break;
      case 'service_update':
        this.handleServiceUpdateNotification(data);
        break;
      default:
        if (config.isDevelopment) {
          console.log('Unknown notification type:', data?.type);
        }
    }
  }

  handleNotificationResponse(response) {
    const { data } = response.notification.request.content;

    // Navigate to appropriate screen based on notification type
    switch (data?.type) {
      case 'new_message':
        // Navigate to messages screen
        this.navigateToScreen('Messages', { messageId: data.messageId });
        break;
      case 'project_update':
        // Navigate to project detail
        this.navigateToScreen('ProjectDetail', { project: { id: data.projectId } });
        break;
      case 'service_update':
        // Navigate to service detail
        this.navigateToScreen('ServiceDetail', { service: { id: data.serviceId } });
        break;
    }
  }

  handleNewMessageNotification(data) {
    // Update message badge count
    this.updateBadgeCount('messages');

    // Trigger any message-specific handlers
    if (this.onNewMessage) {
      this.onNewMessage(data);
    }
  }

  handleProjectUpdateNotification(data) {
    // Trigger project update handlers
    if (this.onProjectUpdate) {
      this.onProjectUpdate(data);
    }
  }

  handleServiceUpdateNotification(data) {
    // Trigger service update handlers
    if (this.onServiceUpdate) {
      this.onServiceUpdate(data);
    }
  }

  navigateToScreen(screenName, params = {}) {
    // This will be set by the navigation system
    if (this.navigationRef) {
      this.navigationRef.navigate(screenName, params);
    }
  }

  async updateBadgeCount(type) {
    try {
      const currentCount = await Notifications.getBadgeCountAsync();
      await Notifications.setBadgeCountAsync(currentCount + 1);
    } catch (error) {
      console.error('Error updating badge count:', error);
    }
  }

  async clearBadgeCount() {
    try {
      await Notifications.setBadgeCountAsync(0);
    } catch (error) {
      console.error('Error clearing badge count:', error);
    }
  }

  // Local notification methods
  async scheduleLocalNotification(title, body, data = {}, trigger = null) {
    try {
      const notificationId = await Notifications.scheduleNotificationAsync({
        content: {
          title,
          body,
          data,
          sound: 'default',
        },
        trigger: trigger || null, // null means immediate
      });

      if (config.isDevelopment) {
        console.log('📅 Local notification scheduled:', notificationId);
      }

      return notificationId;
    } catch (error) {
      console.error('Error scheduling local notification:', error);
      return null;
    }
  }

  async cancelLocalNotification(notificationId) {
    try {
      await Notifications.cancelScheduledNotificationAsync(notificationId);
    } catch (error) {
      console.error('Error canceling notification:', error);
    }
  }

  async cancelAllLocalNotifications() {
    try {
      await Notifications.cancelAllScheduledNotificationsAsync();
    } catch (error) {
      console.error('Error canceling all notifications:', error);
    }
  }

  // Push notification methods
  async sendPushNotification(expoPushToken, title, body, data = {}) {
    const message = {
      to: expoPushToken,
      sound: 'default',
      title,
      body,
      data,
    };

    try {
      const response = await fetch('https://exp.host/--/api/v2/push/send', {
        method: 'POST',
        headers: {
          Accept: 'application/json',
          'Accept-encoding': 'gzip, deflate',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(message),
      });

      const result = await response.json();

      if (config.isDevelopment) {
        console.log('📤 Push notification sent:', result);
      }

      return result;
    } catch (error) {
      console.error('Error sending push notification:', error);
      return null;
    }
  }

  // Notification preferences
  async getNotificationSettings() {
    try {
      const settings = await SecureStore.getItemAsync('notificationSettings');
      return settings ? JSON.parse(settings) : {
        enabled: true,
        sound: true,
        vibration: true,
        newMessages: true,
        projectUpdates: true,
        serviceUpdates: true,
      };
    } catch (error) {
      console.error('Error getting notification settings:', error);
      return {};
    }
  }

  async updateNotificationSettings(settings) {
    try {
      await SecureStore.setItemAsync('notificationSettings', JSON.stringify(settings));

      // Update notification permissions based on settings
      if (!settings.enabled) {
        await this.cancelAllLocalNotifications();
      }

      return true;
    } catch (error) {
      console.error('Error updating notification settings:', error);
      return false;
    }
  }

  // Event handlers (to be set by the app)
  setNavigationRef(navigationRef) {
    this.navigationRef = navigationRef;
  }

  setEventHandlers({ onNewMessage, onProjectUpdate, onServiceUpdate }) {
    this.onNewMessage = onNewMessage;
    this.onProjectUpdate = onProjectUpdate;
    this.onServiceUpdate = onServiceUpdate;
  }

  // Cleanup
  cleanup() {
    if (this.notificationListener) {
      Notifications.removeNotificationSubscription(this.notificationListener);
    }
    if (this.responseListener) {
      Notifications.removeNotificationSubscription(this.responseListener);
    }
  }

  // Get current push token
  getPushToken() {
    return this.expoPushToken;
  }

  // Check if notifications are enabled
  async areNotificationsEnabled() {
    try {
      const { status } = await Notifications.getPermissionsAsync();
      return status === 'granted';
    } catch (error) {
      console.log('📱 Notifications: Could not check permission status');
      return false;
    }
  }

  // Setup local notification channel for Android
  async setupLocalNotificationChannel() {
    try {
      await Notifications.setNotificationChannelAsync('default', {
        name: 'General Notifications',
        importance: Notifications.AndroidImportance.MAX,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: '#FF6B35',
        sound: 'default',
        enableVibrate: true,
        enableLights: true,
        description: 'General notifications for the Flori Construction app',
      });

      // Create additional channels for different notification types
      await Notifications.setNotificationChannelAsync('messages', {
        name: 'New Messages',
        importance: Notifications.AndroidImportance.HIGH,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: '#FF6B35',
        sound: 'default',
        description: 'Notifications for new messages from clients',
      });

      await Notifications.setNotificationChannelAsync('projects', {
        name: 'Project Updates',
        importance: Notifications.AndroidImportance.DEFAULT,
        vibrationPattern: [0, 250],
        lightColor: '#FF6B35',
        sound: 'default',
        description: 'Notifications for project status updates',
      });

      console.log('📱 Notifications: Android notification channels configured');
    } catch (channelError) {
      console.log('📱 Notifications: Could not configure notification channels:', channelError.message);
    }
  }
}

export const notificationService = new NotificationService();
